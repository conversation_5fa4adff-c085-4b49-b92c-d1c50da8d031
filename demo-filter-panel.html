<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо панели фильтров с таймером смещений</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }

        /* Копируем стили из styles.css */
        .bb-filter-panel {
            margin: 20px 0;
            padding: 20px;
            background: rgb(24, 26, 27);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .bb-filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .bb-filter-section {
            min-width: 200px;
            flex: 1;
            margin-bottom: 15px;
        }

        .bb-filter-title {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bb-filter-checkbox {
            width: 16px !important;
            height: 16px !important;
            cursor: pointer !important;
            accent-color: #4D7FF5 !important;
            border: 2px solid #4D7FF5 !important;
            border-radius: 3px !important;
            background: transparent !important;
            position: relative !important;
            opacity: 0.8 !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }

        .bb-filter-checkbox:hover {
            opacity: 1 !important;
            border-color: #8274F6 !important;
        }

        .bb-filter-checkbox:checked {
            background-color: #4D7FF5 !important;
            opacity: 1 !important;
        }

        .bb-filter-checkbox:checked::after {
            content: '✓' !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            color: white !important;
            font-size: 12px !important;
            font-weight: bold !important;
        }

        .bb-filter-checkbox-label {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            color: #fff !important;
            cursor: pointer !important;
            padding: 4px !important;
            border-radius: 4px !important;
            transition: background-color 0.2s ease !important;
        }

        .bb-filter-checkbox-label:hover {
            background-color: rgba(77, 127, 245, 0.1) !important;
        }

        .bb-timer-offset-filter {
            margin-top: 15px;
            padding: 10px;
            background: rgba(77, 127, 245, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(77, 127, 245, 0.3);
        }

        .bb-timer-offset-input {
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .bb-timer-input {
            background: rgb(24, 26, 27) !important;
            color: #fff !important;
            border: 1px solid #444 !important;
            padding: 6px 10px !important;
            border-radius: 4px !important;
            font-family: 'Rajdhani', sans-serif !important;
            font-size: 14px !important;
            width: 120px !important;
            transition: border-color 0.2s ease !important;
        }

        .bb-timer-input:focus {
            outline: none !important;
            border-color: #4D7FF5 !important;
            box-shadow: 0 0 0 2px rgba(77, 127, 245, 0.2) !important;
        }

        .bb-timer-input:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            background: rgb(35, 37, 38) !important;
        }

        .bb-timer-hint {
            color: #888 !important;
            font-size: 12px !important;
            font-style: italic !important;
        }

        .demo-info {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-info">
        <h1>Демонстрация новой функциональности фильтров</h1>
        <p>Добавлен чекбокс "Выбирать также ластов/предластов с таймером" с полем для ввода временных смещений.</p>
        <p><strong>Как это работает:</strong></p>
        <ul>
            <li>Активируйте чекбокс ниже - автоматически установятся значения по умолчанию: 60,75</li>
            <li>Можете изменить временные смещения через запятую (например: 60,105)</li>
            <li>При выборе времени (например, 10:00) будут также показываться Last Boss Kill и Last 2 Boss Kills на 11:00 (+60 мин) и 11:15 (+75 мин)</li>
        </ul>
    </div>

    <div class="bb-filter-panel">
        <div class="bb-filter-section">
            <div class="bb-filter-title">Даты</div>
            <div class="bb-timer-offset-filter">
                <label class="bb-filter-checkbox-label">
                    <input type="checkbox" class="bb-filter-checkbox" id="enableTimerOffset">
                    <span>Выбирать также ластов/предластов с таймером:</span>
                </label>
                <div class="bb-timer-offset-input">
                    <input type="text" id="timerOffsets" class="bb-timer-input" placeholder="60,75" disabled>
                    <span class="bb-timer-hint">минуты через запятую</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Добавляем интерактивность
        const checkbox = document.getElementById('enableTimerOffset');
        const input = document.getElementById('timerOffsets');

        checkbox.addEventListener('change', function() {
            input.disabled = !this.checked;
            if (!this.checked) {
                input.value = '';
            } else {
                // Если чекбокс активируется и поле пустое, устанавливаем значения по умолчанию
                if (!input.value.trim()) {
                    input.value = '60,75';
                }
            }
        });
    </script>
</body>
</html>
