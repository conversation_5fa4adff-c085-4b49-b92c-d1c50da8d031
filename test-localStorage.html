<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест сохранения состояния таймера смещений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 5px;
        }
        .controls {
            margin: 10px 0;
        }
        input[type="checkbox"] {
            margin-right: 8px;
        }
        input[type="text"] {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 5px;
            border-radius: 3px;
        }
        button {
            background: #4D7FF5;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #6B8FF7;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
    </style>
</head>
<body>
    <h1>Тест сохранения состояния таймера смещений</h1>
    
    <div class="test-section">
        <h2>Симуляция элементов фильтра</h2>
        <div class="controls">
            <label>
                <input type="checkbox" id="enableTimerOffset">
                Выбирать также ластов/предластов с таймером:
            </label>
        </div>
        <div class="controls">
            <input type="text" id="timerOffsets" placeholder="60,105" disabled>
            <span style="color: #888; font-size: 12px;">минуты через запятую</span>
        </div>
        
        <div class="controls">
            <button onclick="saveState()">Сохранить состояние</button>
            <button onclick="loadState()">Загрузить состояние</button>
            <button onclick="clearState()">Очистить состояние</button>
            <button onclick="refreshPage()">Обновить страницу</button>
        </div>
    </div>

    <div class="test-section">
        <h2>Информация о сохраненных данных</h2>
        <div id="storageInfo" class="status info">
            Загрузка...
        </div>
    </div>

    <div class="test-section">
        <h2>Инструкции для тестирования</h2>
        <ol>
            <li>Активируйте чекбокс и введите значения в поле (например: 60,105)</li>
            <li>Нажмите "Сохранить состояние"</li>
            <li>Нажмите "Обновить страницу" - состояние должно восстановиться</li>
            <li>Попробуйте "Очистить состояние" и снова обновить страницу</li>
        </ol>
    </div>

    <script>
        const checkbox = document.getElementById('enableTimerOffset');
        const input = document.getElementById('timerOffsets');
        const storageInfo = document.getElementById('storageInfo');

        // Функции для работы с localStorage (копируем логику из filter-utils.js)
        function saveState() {
            localStorage.setItem('bb-timer-offset-enabled', checkbox.checked);
            localStorage.setItem('bb-timer-offsets', input.value);
            updateStorageInfo();
            showStatus('Состояние сохранено!', 'success');
        }

        function loadState() {
            const savedEnabled = localStorage.getItem('bb-timer-offset-enabled') === 'true';
            const savedOffsets = localStorage.getItem('bb-timer-offsets') || '';
            
            checkbox.checked = savedEnabled;
            input.disabled = !savedEnabled;
            input.value = savedOffsets;
            
            updateStorageInfo();
            showStatus('Состояние загружено!', 'success');
        }

        function clearState() {
            localStorage.removeItem('bb-timer-offset-enabled');
            localStorage.removeItem('bb-timer-offsets');
            
            checkbox.checked = false;
            input.disabled = true;
            input.value = '';
            
            updateStorageInfo();
            showStatus('Состояние очищено!', 'success');
        }

        function refreshPage() {
            location.reload();
        }

        function updateStorageInfo() {
            const enabled = localStorage.getItem('bb-timer-offset-enabled');
            const offsets = localStorage.getItem('bb-timer-offsets');
            
            storageInfo.innerHTML = `
                <strong>Сохраненные данные:</strong><br>
                bb-timer-offset-enabled: ${enabled || 'не установлено'}<br>
                bb-timer-offsets: ${offsets || 'не установлено'}
            `;
        }

        function showStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            
            const testSection = document.querySelector('.test-section');
            testSection.appendChild(statusDiv);
            
            setTimeout(() => {
                statusDiv.remove();
            }, 3000);
        }

        // Обработчики событий
        checkbox.addEventListener('change', function() {
            input.disabled = !this.checked;
            if (!this.checked) {
                input.value = '';
            }
        });

        // Автоматическая загрузка состояния при загрузке страницы
        window.addEventListener('load', function() {
            loadState();
        });

        // Обновляем информацию каждую секунду
        setInterval(updateStorageInfo, 1000);
    </script>
</body>
</html>
