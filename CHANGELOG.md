# Changelog - Добавление фильтра ластов/предластов с таймером

## Что было добавлено

### 1. Новые элементы интерфейса в панели фильтров:
- **Чекбокс**: "Выбирать также ластов/предластов с таймером"
- **Поле ввода**: для указания временных смещений через запятую (например: 60,105)

### 2. Логика фильтрации:
- При активном чекбоксе и выборе времени (например, 10:00) дополнительно показываются:
  - События типа "Last Boss Kill"
  - События типа "Last 2 Boss Kills"
  - С временем, увеличенным на указанные смещения (11:00 и 11:45 для примера выше)

### 3. Сохранение состояния:
- **Автоматическое сохранение**: состояние чекбокса и введенных таймеров сохраняется в localStorage
- **Автоматическое восстановление**: при загрузке страницы состояние восстанавливается
- **Ключи localStorage**:
  - `bb-timer-offset-enabled` - состояние чекбокса
  - `bb-timer-offsets` - введенные временные смещения

### 3. Технические детали:

#### Файлы изменены:
- `filter-utils.js` - основная логика фильтрации
- `styles.css` - стили для новых элементов

#### Новые функции:
- `parseTimeAndAddOffset(timeStr, offsetMinutes)` - парсинг времени и добавление смещения

#### Изменения в существующих функциях:
- `createFilterPanel()` - добавлены новые элементы интерфейса + восстановление сохраненного состояния
- `applyFilters()` - добавлена логика проверки временных смещений
- `resetFilters()` - сброс новых элементов + очистка localStorage

### 4. Стили:
- `.bb-timer-offset-filter` - контейнер для новой функциональности
- `.bb-timer-input` - стили для поля ввода смещений
- `.bb-timer-hint` - подсказка для пользователя

## Как использовать:

1. Откройте панель фильтров на странице BlazingBoost
2. Найдите секцию "Даты" 
3. Активируйте чекбокс "Выбирать также ластов/предластов с таймером"
4. Введите временные смещения через запятую (например: 60,105)
5. Выберите нужное время в фильтре времени
6. В результатах будут показаны:
   - Обычные события на выбранное время
   - Last Boss Kill и Last 2 Boss Kills на время + смещения

## Пример работы:
- Выбрано время: 10:00
- Смещения: 60,105
- Результат: показываются события на 10:00 + ласты/предласты на 11:00 и 11:45

## Файлы для тестирования:
- `test-timer-offset.html` - тест функции parseTimeAndAddOffset
- `demo-filter-panel.html` - демонстрация интерфейса
- `test-localStorage.html` - тест сохранения и восстановления состояния
