<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест значений по умолчанию для таймера смещений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 5px;
        }
        .controls {
            margin: 10px 0;
        }
        input[type="checkbox"] {
            margin-right: 8px;
        }
        input[type="text"] {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 5px;
            border-radius: 3px;
            width: 120px;
        }
        button {
            background: #4D7FF5;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #6B8FF7;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #FFC107;
        }
    </style>
</head>
<body>
    <h1>Тест значений по умолчанию для таймера смещений</h1>
    
    <div class="test-section">
        <h2>Симуляция поведения расширения</h2>
        <div class="controls">
            <label>
                <input type="checkbox" id="enableTimerOffset">
                Выбирать также ластов/предластов с таймером:
            </label>
        </div>
        <div class="controls">
            <input type="text" id="timerOffsets" placeholder="60,75" disabled>
            <span style="color: #888; font-size: 12px;">минуты через запятую</span>
        </div>
        
        <div class="controls">
            <button onclick="clearAll()">Очистить всё</button>
            <button onclick="simulateFirstInstall()">Симулировать первую установку</button>
            <button onclick="testDefaultValues()">Тест значений по умолчанию</button>
        </div>
    </div>

    <div class="test-section">
        <h2>Результаты тестов</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>Сценарии тестирования</h2>
        <div class="status info">
            <strong>Тест 1:</strong> Нажмите "Симулировать первую установку", затем активируйте чекбокс - должно появиться "60,75"<br>
            <strong>Тест 2:</strong> Введите свои значения, деактивируйте и снова активируйте чекбокс - должны остаться ваши значения<br>
            <strong>Тест 3:</strong> Очистите поле, деактивируйте и снова активируйте - должно появиться "60,75"
        </div>
    </div>

    <script>
        const checkbox = document.getElementById('enableTimerOffset');
        const input = document.getElementById('timerOffsets');
        const resultsDiv = document.getElementById('testResults');

        // Симулируем логику из filter-utils.js
        checkbox.addEventListener('change', function(e) {
            input.disabled = !e.target.checked;
            
            if (!e.target.checked) {
                input.value = '';
            } else {
                // Если чекбокс активируется и поле пустое, устанавливаем значения по умолчанию
                if (!input.value.trim()) {
                    input.value = '60,75';
                    showResult('✅ Установлены значения по умолчанию: 60,75', 'success');
                } else {
                    showResult('ℹ️ Сохранены существующие значения: ' + input.value, 'info');
                }
            }
            
            // Симулируем сохранение в localStorage
            localStorage.setItem('bb-timer-offset-enabled', e.target.checked);
            localStorage.setItem('bb-timer-offsets', input.value);
        });

        function clearAll() {
            localStorage.removeItem('bb-timer-offset-enabled');
            localStorage.removeItem('bb-timer-offsets');
            checkbox.checked = false;
            input.disabled = true;
            input.value = '';
            showResult('🧹 Всё очищено', 'warning');
        }

        function simulateFirstInstall() {
            clearAll();
            showResult('🆕 Симулирована первая установка расширения', 'info');
        }

        function testDefaultValues() {
            const tests = [
                {
                    name: 'Тест 1: Пустое поле при активации',
                    setup: () => {
                        input.value = '';
                        checkbox.checked = false;
                    },
                    action: () => {
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change'));
                    },
                    expected: '60,75'
                },
                {
                    name: 'Тест 2: Поле с пробелами при активации',
                    setup: () => {
                        input.value = '   ';
                        checkbox.checked = false;
                    },
                    action: () => {
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change'));
                    },
                    expected: '60,75'
                },
                {
                    name: 'Тест 3: Поле с существующими значениями',
                    setup: () => {
                        input.value = '90,120';
                        checkbox.checked = false;
                    },
                    action: () => {
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change'));
                    },
                    expected: '90,120'
                }
            ];

            resultsDiv.innerHTML = '<h3>Результаты автоматических тестов:</h3>';
            
            tests.forEach((test, index) => {
                test.setup();
                test.action();
                
                const passed = input.value === test.expected;
                const status = passed ? 'success' : 'warning';
                const icon = passed ? '✅' : '❌';
                
                showResult(`${icon} ${test.name}: Ожидалось "${test.expected}", получено "${input.value}"`, status);
            });
        }

        function showResult(message, type) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        // Восстанавливаем состояние при загрузке
        window.addEventListener('load', function() {
            const savedEnabled = localStorage.getItem('bb-timer-offset-enabled') === 'true';
            const savedOffsets = localStorage.getItem('bb-timer-offsets') || '';
            
            checkbox.checked = savedEnabled;
            input.disabled = !savedEnabled;
            input.value = savedOffsets;
        });
    </script>
</body>
</html>
