<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест функции таймера смещений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #2a2a2a;
            border-radius: 5px;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>Тест функции parseTimeAndAddOffset</h1>
    <div id="results"></div>

    <script>
        // Копируем функцию из filter-utils.js
        function parseTimeAndAddOffset(timeStr, offsetMinutes) {
            const [hours, minutes] = timeStr.split(':').map(Number);
            const totalMinutes = hours * 60 + minutes + offsetMinutes;
            const newHours = Math.floor(totalMinutes / 60) % 24;
            const newMinutes = totalMinutes % 60;
            return `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`;
        }

        // Тестовые случаи
        const testCases = [
            { input: '10:00', offset: 60, expected: '11:00', description: '10:00 + 60 минут = 11:00' },
            { input: '10:00', offset: 105, expected: '11:45', description: '10:00 + 105 минут = 11:45' },
            { input: '23:30', offset: 60, expected: '00:30', description: '23:30 + 60 минут = 00:30 (переход через полночь)' },
            { input: '09:15', offset: 45, expected: '10:00', description: '09:15 + 45 минут = 10:00' },
            { input: '14:30', offset: 90, expected: '16:00', description: '14:30 + 90 минут = 16:00' },
            { input: '22:00', offset: 180, expected: '01:00', description: '22:00 + 180 минут = 01:00 (переход через полночь)' }
        ];

        const resultsDiv = document.getElementById('results');

        testCases.forEach((testCase, index) => {
            const result = parseTimeAndAddOffset(testCase.input, testCase.offset);
            const isSuccess = result === testCase.expected;
            
            const testDiv = document.createElement('div');
            testDiv.className = `test-case ${isSuccess ? 'success' : 'error'}`;
            testDiv.innerHTML = `
                <strong>Тест ${index + 1}:</strong> ${testCase.description}<br>
                <strong>Результат:</strong> ${result}<br>
                <strong>Ожидалось:</strong> ${testCase.expected}<br>
                <strong>Статус:</strong> ${isSuccess ? '✅ ПРОЙДЕН' : '❌ ПРОВАЛЕН'}
            `;
            
            resultsDiv.appendChild(testDiv);
        });

        // Общий результат
        const passedTests = testCases.filter((testCase, index) => {
            const result = parseTimeAndAddOffset(testCase.input, testCase.offset);
            return result === testCase.expected;
        }).length;

        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'test-case';
        summaryDiv.innerHTML = `
            <h2>Итоги тестирования</h2>
            <strong>Пройдено тестов:</strong> ${passedTests} из ${testCases.length}<br>
            <strong>Статус:</strong> ${passedTests === testCases.length ? '✅ ВСЕ ТЕСТЫ ПРОЙДЕНЫ' : '❌ ЕСТЬ ОШИБКИ'}
        `;
        
        resultsDiv.appendChild(summaryDiv);
    </script>
</body>
</html>
